# OCR Setup Instructions

The OCR-based query processing endpoint requires Tesseract OCR to be installed on your system.

## Quick Setup

### Option 1: Run the Installation Helper
```bash
python ds_task_1ab/install_tesseract.py
```

### Option 2: Manual Installation

#### Windows
1. Download Tesseract installer from: https://github.com/UB-Mannheim/tesseract/wiki
2. Choose the appropriate version for your system (64-bit or 32-bit)
3. Run the installer with default settings
4. Restart your application

#### macOS
```bash
brew install tesseract
```

#### Ubuntu/Debian
```bash
sudo apt update
sudo apt install tesseract-ocr
```

#### CentOS/RHEL/Fedora
```bash
sudo yum install tesseract
# or
sudo dnf install tesseract
```

## Verification

After installation, restart your Flask application. You should see:
```
OCR query service initialized successfully
```

Instead of:
```
Warning: Could not initialize OCR service: Tesseract OCR is not properly installed
```

## Troubleshooting

### Windows Issues
If you still get errors after installation:
1. Check if Tessera<PERSON> is in your PATH
2. Add `C:\Program Files\Tesseract-OCR` to your system PATH
3. Restart your command prompt/IDE

### Common Issues
- **Permission errors**: Run as administrator (Windows) or use sudo (Linux/macOS)
- **Path issues**: Ensure Tesseract is in your system PATH
- **Version conflicts**: Uninstall old versions before installing new ones

## Testing

You can test the OCR functionality by:
1. Starting your Flask application
2. Going to the main page
3. Using the "OCR-Based Query Processing" section
4. Uploading an image with text

## Supported Image Formats

- JPG/JPEG
- PNG
- BMP
- TIFF/TIF
- GIF

Maximum file size: 10MB
